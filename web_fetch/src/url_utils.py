"""
URL validation and analysis utilities.

This module provides utility functions for URL validation, normalization,
and analysis, as well as response header analysis and content type detection.
"""

from __future__ import annotations

from typing import Dict, Optional


def is_valid_url(url: str) -> bool:
    """
    Check if a URL is valid.

    Args:
        url: URL string to validate

    Returns:
        True if URL is valid, False otherwise
    """
    from ..utils import URLValidator
    return URLValidator.is_valid_url(url)


def normalize_url(url: str, base_url: Optional[str] = None) -> str:
    """
    Normalize a URL by resolving relative paths and cleaning up.

    Args:
        url: URL to normalize
        base_url: Base URL for resolving relative URLs

    Returns:
        Normalized URL string
    """
    from ..utils import URLValidator
    return URLValidator.normalize_url(url, base_url)


def analyze_url(url: str):  # Returns URLAnalysis
    """
    Perform comprehensive URL analysis.

    Args:
        url: URL to analyze

    Returns:
        URLAnalysis object with detailed information
    """
    from ..utils import URLValidator
    return URLValidator.analyze_url(url)


def analyze_headers(headers: Dict[str, str]):  # Returns HeaderAnalysis
    """
    Analyze HTTP response headers.

    Args:
        headers: Dictionary of response headers

    Returns:
        HeaderAnalysis object with parsed header information
    """
    from ..utils import ResponseAnalyzer
    return ResponseAnalyzer.analyze_headers(headers)


def detect_content_type(headers: Dict[str, str], content: bytes) -> str:
    """
    Detect content type from headers and content.

    Args:
        headers: Response headers
        content: Response content bytes

    Returns:
        Detected content type string
    """
    from ..utils import ResponseAnalyzer
    return ResponseAnalyzer.detect_content_type(headers, content)
