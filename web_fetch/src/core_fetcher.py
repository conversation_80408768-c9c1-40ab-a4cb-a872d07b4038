"""
Core async web fetcher implementation using AIOHTTP.

This module provides the main WebFetcher class that handles asynchronous HTTP requests
with proper session management, connection pooling, and modern Python features.
"""

from __future__ import annotations

import asyncio
import json
import time
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional, Union

import aiohttp
from aiohttp import ClientSession, ClientTimeout, TCPConnector
from bs4 import BeautifulSoup

from ..exceptions import (
    ContentError,
    ErrorHandler,
    HTTPError,
    NetworkError,
    TimeoutError,
    WebFetchError,
)
from ..models import (
    BatchFetchRequest,
    BatchFetchResult,
    ContentType,
    FetchConfig,
    FetchRequest,
    FetchResult,
    RetryStrategy,
)


class WebFetcher:
    """
    Async web fetcher with modern Python features and AIOHTTP best practices.
    
    Features:
    - Async/await syntax for concurrent requests
    - Proper session management with context managers
    - Connection pooling and timeout configuration
    - Retry logic with exponential backoff
    - Content parsing (J<PERSON><PERSON>, HTML, text)
    - Comprehensive error handling
    """
    
    def __init__(self, config: Optional[FetchConfig] = None):
        """Initialize the WebFetcher with configuration."""
        self.config = config or FetchConfig()
        self._session: Optional[ClientSession] = None
        self._semaphore: Optional[asyncio.Semaphore] = None
    
    async def __aenter__(self) -> WebFetcher:
        """Async context manager entry."""
        await self._create_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Async context manager exit with proper cleanup."""
        await self.close()
    
    async def _create_session(self) -> None:
        """Create aiohttp session with proper configuration."""
        if self._session is not None:
            return
        
        # Configure timeouts
        timeout = ClientTimeout(
            total=self.config.total_timeout,
            connect=self.config.connect_timeout,
            sock_read=self.config.read_timeout
        )
        
        # Configure TCP connector for connection pooling
        connector = TCPConnector(
            limit=self.config.max_connections_per_host * 10,  # Total connection pool
            limit_per_host=self.config.max_connections_per_host,
            ssl=self.config.verify_ssl,
            enable_cleanup_closed=True
        )
        
        # Create session with configuration
        self._session = ClientSession(
            timeout=timeout,
            connector=connector,
            headers=self.config.headers.to_dict(),
            raise_for_status=False  # We'll handle status codes manually
        )
        
        # Create semaphore for concurrency control
        self._semaphore = asyncio.Semaphore(self.config.max_concurrent_requests)
    
    async def close(self) -> None:
        """Close the session and cleanup resources."""
        if self._session:
            await self._session.close()
            self._session = None
        self._semaphore = None

    async def fetch_single(self, request: FetchRequest) -> FetchResult:
        """
        Fetch a single URL with retry logic and error handling.

        Args:
            request: FetchRequest object containing URL and options

        Returns:
            FetchResult object with response data and metadata
        """
        if not self._session:
            await self._create_session()

        start_time = time.time()
        last_error: Optional[str] = None

        for attempt in range(self.config.max_retries + 1):
            try:
                if self._semaphore is None:
                    raise WebFetchError("Session not properly initialized")

                async with self._semaphore:  # Control concurrency
                    result = await self._execute_request(request, attempt)
                    result.response_time = time.time() - start_time
                    return result

            except Exception as e:
                # Convert to appropriate WebFetchError subclass
                web_error = ErrorHandler.handle_aiohttp_error(e, str(request.url))
                last_error = str(web_error)

                # Check if error is retryable
                if attempt < self.config.max_retries and ErrorHandler.is_retryable_error(web_error):
                    delay = ErrorHandler.get_retry_delay(web_error, attempt, self.config.retry_delay)
                    await asyncio.sleep(delay)
                else:
                    # Final attempt failed or error is not retryable
                    status_code = getattr(web_error, 'status_code', 0)
                    return FetchResult(
                        url=str(request.url),
                        status_code=status_code,
                        headers={},
                        content=None,
                        content_type=request.content_type,
                        response_time=time.time() - start_time,
                        timestamp=datetime.now(),
                        error=last_error,
                        retry_count=attempt
                    )

        # This should never be reached, but included for completeness
        return FetchResult(
            url=str(request.url),
            status_code=0,
            headers={},
            content=None,
            content_type=request.content_type,
            response_time=time.time() - start_time,
            timestamp=datetime.now(),
            error="Maximum retries exceeded",
            retry_count=self.config.max_retries
        )

    async def _execute_request(self, request: FetchRequest, attempt: int) -> FetchResult:
        """Execute a single HTTP request."""
        if self._session is None:
            raise WebFetchError("Session not properly initialized")

        # Prepare request parameters
        kwargs = {
            'method': request.method,
            'url': str(request.url),
            'headers': request.headers or {},
            'params': request.params,
        }

        # Add data for POST/PUT requests
        if request.data is not None:
            if isinstance(request.data, dict):
                kwargs['json'] = request.data
            elif isinstance(request.data, (str, bytes)):
                kwargs['data'] = request.data

        # Override timeout if specified
        if request.timeout_override:
            kwargs['timeout'] = ClientTimeout(total=request.timeout_override)

        async with self._session.request(**kwargs) as response:
            # Check for server errors that should be retried
            if response.status >= 500:
                from ..exceptions import ServerError
                raise ServerError(
                    f"Server error: {response.status} {response.reason}",
                    status_code=response.status,
                    url=str(request.url),
                    headers=dict(response.headers)
                )

            # Read response content
            content_bytes = await response.read()

            # Check response size
            if len(content_bytes) > self.config.max_response_size:
                from ..exceptions import ContentError
                raise ContentError(
                    f"Response size {len(content_bytes)} exceeds maximum {self.config.max_response_size}",
                    url=str(request.url),
                    content_length=len(content_bytes)
                )

            # Parse content based on requested type
            parsed_content = await self._parse_content(
                content_bytes,
                request.content_type
            )

            return FetchResult(
                url=str(request.url),
                status_code=response.status,
                headers=dict(response.headers),
                content=parsed_content,
                content_type=request.content_type,
                response_time=0.0,  # Will be set by caller
                timestamp=datetime.now(),
                retry_count=attempt
            )

    async def _parse_content(
        self,
        content_bytes: bytes,
        requested_type: ContentType
    ) -> Union[str, bytes, Dict[str, Any], None]:
        """Parse response content based on requested content type."""
        if not content_bytes:
            return None

        match requested_type:
            case ContentType.RAW:
                return content_bytes

            case ContentType.TEXT:
                try:
                    return content_bytes.decode('utf-8')
                except UnicodeDecodeError:
                    # Try common encodings
                    for encoding in ['latin1', 'cp1252', 'iso-8859-1']:
                        try:
                            return content_bytes.decode(encoding)
                        except UnicodeDecodeError:
                            continue
                    return content_bytes.decode('utf-8', errors='replace')

            case ContentType.JSON:
                try:
                    text_content = content_bytes.decode('utf-8')
                    return json.loads(text_content)
                except (UnicodeDecodeError, json.JSONDecodeError) as e:
                    from ..exceptions import ContentError
                    raise ContentError(
                        f"Failed to parse JSON content: {e}",
                        content_type="application/json"
                    )

            case ContentType.HTML:
                try:
                    text_content = content_bytes.decode('utf-8')
                    soup = BeautifulSoup(text_content, 'lxml')
                    return {
                        'title': soup.title.string if soup.title else None,
                        'text': soup.get_text(strip=True),
                        'links': [a.get('href') for a in soup.find_all('a', href=True)],
                        'images': [img.get('src') for img in soup.find_all('img', src=True)],
                        'raw_html': text_content
                    }
                except Exception as e:
                    raise WebFetchError(f"Failed to parse HTML content: {e}")

            case _:
                return content_bytes.decode('utf-8', errors='replace')

    def _calculate_retry_delay(self, attempt: int) -> float:
        """Calculate delay for retry based on strategy."""
        base_delay = self.config.retry_delay

        match self.config.retry_strategy:
            case RetryStrategy.NONE:
                return 0.0
            case RetryStrategy.LINEAR:
                return base_delay * (attempt + 1)
            case RetryStrategy.EXPONENTIAL:
                return base_delay * (2 ** attempt)
            case _:
                return base_delay

    async def fetch_batch(self, batch_request: BatchFetchRequest) -> BatchFetchResult:
        """
        Fetch multiple URLs concurrently.

        Args:
            batch_request: BatchFetchRequest containing multiple URLs and config

        Returns:
            BatchFetchResult with all individual results and summary statistics
        """
        if not self._session:
            await self._create_session()

        start_time = time.time()

        # Create tasks for concurrent execution
        tasks = [
            self.fetch_single(request)
            for request in batch_request.requests
        ]

        # Execute all requests concurrently
        results = await asyncio.gather(*tasks, return_exceptions=False)

        total_time = time.time() - start_time

        return BatchFetchResult.from_results(results, total_time)

    @asynccontextmanager
    async def session_context(self) -> AsyncGenerator[WebFetcher, None]:
        """
        Context manager for session lifecycle management.

        Usage:
            async with WebFetcher().session_context() as fetcher:
                result = await fetcher.fetch_single(request)
        """
        try:
            await self._create_session()
            yield self
        finally:
            await self.close()
