"""
Protocol implementations for the web_fetch library.

This package contains protocol-specific implementations for HTTP and FTP,
organized into separate modules for better maintainability.
"""

# Import all protocol implementations
from .http import *
from .ftp import *

__all__ = [
    # HTTP protocol
    "WebFetcher",
    "StreamingWebFetcher", 
    "fetch_url",
    "fetch_urls",
    "download_file",
    "fetch_with_cache",
    
    # FTP protocol
    "FTPFetcher",
    "ftp_download_file",
    "ftp_download_batch", 
    "ftp_list_directory",
    "ftp_get_file_info",
]
