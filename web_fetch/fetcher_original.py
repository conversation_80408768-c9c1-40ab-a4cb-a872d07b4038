"""
Core async web fetcher implementation using AIOHTTP.

This module provides the main WebFetcher class that handles asynchronous HTTP requests
with proper session management, connection pooling, and modern Python features.
"""

from __future__ import annotations

import asyncio
import json
import time
from contextlib import asynccontextmanager
from datetime import datetime
from pathlib import Path
from typing import Any, Async<PERSON>enerator, Callable, Dict, List, Optional, Union

import aiofiles
import aiohttp
from aiohttp import ClientSession, ClientTimeout, TCPConnector
from bs4 import BeautifulSoup

from .exceptions import (
    ContentError,
    ErrorHandler,
    HTTPError,
    NetworkError,
    TimeoutError,
    WebFetchError,
)
from .models import (
    BatchFetchRequest,
    BatchFetchResult,
    ContentType,
    FetchConfig,
    FetchRequest,
    FetchResult,
    ProgressInfo,
    RetryStrategy,
    StreamingConfig,
    StreamRequest,
    StreamResult,
)
from .utils import RateLimiter, SimpleCache


class WebFetcher:
    """
    Async web fetcher with modern Python features and AIOHTTP best practices.
    
    Features:
    - Async/await syntax for concurrent requests
    - Proper session management with context managers
    - Connection pooling and timeout configuration
    - Retry logic with exponential backoff
    - Content parsing (JSON, HTML, text)
    - Comprehensive error handling
    """
    
    def __init__(self, config: Optional[FetchConfig] = None):
        """Initialize the WebFetcher with configuration."""
        self.config = config or FetchConfig()
        self._session: Optional[ClientSession] = None
        self._semaphore: Optional[asyncio.Semaphore] = None
    
    async def __aenter__(self) -> WebFetcher:
        """Async context manager entry."""
        await self._create_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Async context manager exit with proper cleanup."""
        await self.close()
    
    async def _create_session(self) -> None:
        """Create aiohttp session with proper configuration."""
        if self._session is not None:
            return
        
        # Configure timeouts
        timeout = ClientTimeout(
            total=self.config.total_timeout,
            connect=self.config.connect_timeout,
            sock_read=self.config.read_timeout
        )
        
        # Configure TCP connector for connection pooling
        connector = TCPConnector(
            limit=self.config.max_connections_per_host * 10,  # Total connection pool
            limit_per_host=self.config.max_connections_per_host,
            ssl=self.config.verify_ssl,
            enable_cleanup_closed=True
        )
        
        # Create session with configuration
        self._session = ClientSession(
            timeout=timeout,
            connector=connector,
            headers=self.config.headers.to_dict(),
            raise_for_status=False  # We'll handle status codes manually
        )
        
        # Create semaphore for concurrency control
        self._semaphore = asyncio.Semaphore(self.config.max_concurrent_requests)
    
    async def close(self) -> None:
        """Close the session and cleanup resources."""
        if self._session:
            await self._session.close()
            self._session = None
        self._semaphore = None
    
    async def fetch_single(self, request: FetchRequest) -> FetchResult:
        """
        Fetch a single URL with retry logic and error handling.
        
        Args:
            request: FetchRequest object containing URL and options
            
        Returns:
            FetchResult object with response data and metadata
        """
        if not self._session:
            await self._create_session()
        
        start_time = time.time()
        last_error: Optional[str] = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                if self._semaphore is None:
                    raise WebFetchError("Session not properly initialized")

                async with self._semaphore:  # Control concurrency
                    result = await self._execute_request(request, attempt)
                    result.response_time = time.time() - start_time
                    return result

            except Exception as e:
                # Convert to appropriate WebFetchError subclass
                web_error = ErrorHandler.handle_aiohttp_error(e, str(request.url))
                last_error = str(web_error)

                # Check if error is retryable
                if attempt < self.config.max_retries and ErrorHandler.is_retryable_error(web_error):
                    delay = ErrorHandler.get_retry_delay(web_error, attempt, self.config.retry_delay)
                    await asyncio.sleep(delay)
                else:
                    # Final attempt failed or error is not retryable
                    status_code = getattr(web_error, 'status_code', 0)
                    return FetchResult(
                        url=str(request.url),
                        status_code=status_code,
                        headers={},
                        content=None,
                        content_type=request.content_type,
                        response_time=time.time() - start_time,
                        timestamp=datetime.now(),
                        error=last_error,
                        retry_count=attempt
                    )
        
        # This should never be reached, but included for completeness
        return FetchResult(
            url=str(request.url),
            status_code=0,
            headers={},
            content=None,
            content_type=request.content_type,
            response_time=time.time() - start_time,
            timestamp=datetime.now(),
            error="Maximum retries exceeded",
            retry_count=self.config.max_retries
        )
    
    async def _execute_request(self, request: FetchRequest, attempt: int) -> FetchResult:
        """Execute a single HTTP request."""
        if self._session is None:
            raise WebFetchError("Session not properly initialized")

        # Prepare request parameters
        kwargs = {
            'method': request.method,
            'url': str(request.url),
            'headers': request.headers or {},
            'params': request.params,
        }

        # Add data for POST/PUT requests
        if request.data is not None:
            if isinstance(request.data, dict):
                kwargs['json'] = request.data
            elif isinstance(request.data, (str, bytes)):
                kwargs['data'] = request.data

        # Override timeout if specified
        if request.timeout_override:
            kwargs['timeout'] = ClientTimeout(total=request.timeout_override)

        async with self._session.request(**kwargs) as response:
            # Check for server errors that should be retried
            if response.status >= 500:
                from .exceptions import ServerError
                raise ServerError(
                    f"Server error: {response.status} {response.reason}",
                    status_code=response.status,
                    url=str(request.url),
                    headers=dict(response.headers)
                )

            # Read response content
            content_bytes = await response.read()

            # Check response size
            if len(content_bytes) > self.config.max_response_size:
                from .exceptions import ContentError
                raise ContentError(
                    f"Response size {len(content_bytes)} exceeds maximum {self.config.max_response_size}",
                    url=str(request.url),
                    content_length=len(content_bytes)
                )

            # Parse content based on requested type
            parsed_content = await self._parse_content(
                content_bytes,
                request.content_type
            )

            return FetchResult(
                url=str(request.url),
                status_code=response.status,
                headers=dict(response.headers),
                content=parsed_content,
                content_type=request.content_type,
                response_time=0.0,  # Will be set by caller
                timestamp=datetime.now(),
                retry_count=attempt
            )

    async def _parse_content(
        self,
        content_bytes: bytes,
        requested_type: ContentType
    ) -> Union[str, bytes, Dict[str, Any], None]:
        """Parse response content based on requested content type."""
        if not content_bytes:
            return None

        match requested_type:
            case ContentType.RAW:
                return content_bytes

            case ContentType.TEXT:
                try:
                    return content_bytes.decode('utf-8')
                except UnicodeDecodeError:
                    # Try common encodings
                    for encoding in ['latin1', 'cp1252', 'iso-8859-1']:
                        try:
                            return content_bytes.decode(encoding)
                        except UnicodeDecodeError:
                            continue
                    return content_bytes.decode('utf-8', errors='replace')

            case ContentType.JSON:
                try:
                    text_content = content_bytes.decode('utf-8')
                    return json.loads(text_content)
                except (UnicodeDecodeError, json.JSONDecodeError) as e:
                    from .exceptions import ContentError
                    raise ContentError(
                        f"Failed to parse JSON content: {e}",
                        content_type="application/json"
                    )

            case ContentType.HTML:
                try:
                    text_content = content_bytes.decode('utf-8')
                    soup = BeautifulSoup(text_content, 'lxml')
                    return {
                        'title': soup.title.string if soup.title else None,
                        'text': soup.get_text(strip=True),
                        'links': [a.get('href') for a in soup.find_all('a', href=True)],
                        'images': [img.get('src') for img in soup.find_all('img', src=True)],
                        'raw_html': text_content
                    }
                except Exception as e:
                    raise WebFetchError(f"Failed to parse HTML content: {e}")

            case _:
                return content_bytes.decode('utf-8', errors='replace')

    def _calculate_retry_delay(self, attempt: int) -> float:
        """Calculate delay for retry based on strategy."""
        base_delay = self.config.retry_delay

        match self.config.retry_strategy:
            case RetryStrategy.NONE:
                return 0.0
            case RetryStrategy.LINEAR:
                return base_delay * (attempt + 1)
            case RetryStrategy.EXPONENTIAL:
                return base_delay * (2 ** attempt)
            case _:
                return base_delay

    async def fetch_batch(self, batch_request: BatchFetchRequest) -> BatchFetchResult:
        """
        Fetch multiple URLs concurrently.

        Args:
            batch_request: BatchFetchRequest containing multiple URLs and config

        Returns:
            BatchFetchResult with all individual results and summary statistics
        """
        if not self._session:
            await self._create_session()

        start_time = time.time()

        # Create tasks for concurrent execution
        tasks = [
            self.fetch_single(request)
            for request in batch_request.requests
        ]

        # Execute all requests concurrently
        results = await asyncio.gather(*tasks, return_exceptions=False)

        total_time = time.time() - start_time

        return BatchFetchResult.from_results(results, total_time)

    @asynccontextmanager
    async def session_context(self) -> AsyncGenerator[WebFetcher, None]:
        """
        Context manager for session lifecycle management.

        Usage:
            async with WebFetcher().session_context() as fetcher:
                result = await fetcher.fetch_single(request)
        """
        try:
            await self._create_session()
            yield self
        finally:
            await self.close()


# Convenience functions for quick usage
async def fetch_url(
    url: str,
    content_type: ContentType = ContentType.TEXT,
    config: Optional[FetchConfig] = None
) -> FetchResult:
    """
    Convenience function to fetch a single URL.

    Args:
        url: URL to fetch
        content_type: How to parse the response content
        config: Optional configuration

    Returns:
        FetchResult with response data
    """
    request = FetchRequest(url=url, content_type=content_type)

    async with WebFetcher(config) as fetcher:
        return await fetcher.fetch_single(request)


async def fetch_urls(
    urls: List[str],
    content_type: ContentType = ContentType.TEXT,
    config: Optional[FetchConfig] = None
) -> BatchFetchResult:
    """
    Convenience function to fetch multiple URLs concurrently.

    Args:
        urls: List of URLs to fetch
        content_type: How to parse the response content
        config: Optional configuration

    Returns:
        BatchFetchResult with all results and statistics
    """
    requests = [FetchRequest(url=url, content_type=content_type) for url in urls]
    batch_request = BatchFetchRequest(requests=requests, config=config)

    async with WebFetcher(config) as fetcher:
        return await fetcher.fetch_batch(batch_request)


# Streaming functionality

class StreamingWebFetcher(WebFetcher):
    """
    Extended WebFetcher with streaming capabilities.

    Adds streaming support for large files and continuous data streams
    without loading everything into memory.
    """

    def __init__(self, config: Optional[FetchConfig] = None):
        """Initialize the StreamingWebFetcher with configuration."""
        super().__init__(config)
        self._rate_limiter: Optional[RateLimiter] = None
        self._cache: Optional[SimpleCache] = None

    async def stream_fetch(
        self,
        request: StreamRequest,
        progress_callback: Optional[Callable[[ProgressInfo], None]] = None
    ) -> StreamResult:
        """
        Stream fetch a URL with chunked reading and progress tracking.

        Args:
            request: StreamRequest object containing URL and streaming options
            progress_callback: Optional callback for progress updates

        Returns:
            StreamResult object with streaming metadata
        """
        if not self._session:
            await self._create_session()

        start_time = time.time()
        bytes_downloaded = 0
        chunk_count = 0
        last_progress_time = start_time

        # Prepare request parameters
        kwargs = {
            'method': request.method,
            'url': str(request.url),
            'headers': request.headers or {},
        }

        # Add data for POST/PUT requests
        if request.data is not None:
            if isinstance(request.data, dict):
                kwargs['json'] = request.data
            elif isinstance(request.data, (str, bytes)):
                kwargs['data'] = request.data

        # Override timeout if specified
        if request.timeout_override:
            kwargs['timeout'] = ClientTimeout(total=request.timeout_override)

        try:
            if self._session is None:
                raise WebFetchError("Session not properly initialized")

            async with self._session.request(**kwargs) as response:
                # Get content length if available
                total_bytes = None
                if 'content-length' in response.headers:
                    try:
                        total_bytes = int(response.headers['content-length'])
                    except ValueError:
                        pass

                # Check file size limit
                if (request.streaming_config.max_file_size and
                    total_bytes and
                    total_bytes > request.streaming_config.max_file_size):
                    raise WebFetchError(f"File size {total_bytes} exceeds limit {request.streaming_config.max_file_size}")

                # Open output file if specified
                output_file = None
                if request.output_path:
                    request.output_path.parent.mkdir(parents=True, exist_ok=True)
                    output_file = await aiofiles.open(request.output_path, 'wb')

                try:
                    # Stream content in chunks
                    async for chunk in response.content.iter_chunked(request.streaming_config.chunk_size):
                        if not chunk:
                            break

                        bytes_downloaded += len(chunk)
                        chunk_count += 1

                        # Write to file if specified
                        if output_file:
                            await output_file.write(chunk)

                        # Update progress
                        current_time = time.time()
                        elapsed_time = current_time - start_time

                        if (request.streaming_config.enable_progress and
                            progress_callback and
                            (current_time - last_progress_time) >= request.streaming_config.progress_interval):

                            download_speed = bytes_downloaded / elapsed_time if elapsed_time > 0 else 0
                            percentage = None
                            eta = None

                            if total_bytes:
                                percentage = (bytes_downloaded / total_bytes) * 100
                                if download_speed > 0:
                                    remaining_bytes = total_bytes - bytes_downloaded
                                    eta = remaining_bytes / download_speed

                            progress_info = ProgressInfo(
                                bytes_downloaded=bytes_downloaded,
                                total_bytes=total_bytes,
                                chunk_count=chunk_count,
                                elapsed_time=elapsed_time,
                                download_speed=download_speed,
                                eta=eta,
                                percentage=percentage
                            )

                            progress_callback(progress_info)
                            last_progress_time = current_time

                finally:
                    if output_file:
                        await output_file.close()

                # Create final progress info
                final_elapsed = time.time() - start_time
                final_speed = bytes_downloaded / final_elapsed if final_elapsed > 0 else 0
                final_percentage = 100.0 if total_bytes and bytes_downloaded >= total_bytes else None

                final_progress = ProgressInfo(
                    bytes_downloaded=bytes_downloaded,
                    total_bytes=total_bytes,
                    chunk_count=chunk_count,
                    elapsed_time=final_elapsed,
                    download_speed=final_speed,
                    eta=0.0 if final_percentage == 100.0 else None,
                    percentage=final_percentage
                )

                # Call progress callback one final time if enabled
                if request.streaming_config.enable_progress and progress_callback:
                    progress_callback(final_progress)

                return StreamResult(
                    url=str(request.url),
                    status_code=response.status,
                    headers=dict(response.headers),
                    bytes_downloaded=bytes_downloaded,
                    total_bytes=total_bytes,
                    output_path=request.output_path,
                    response_time=final_elapsed,
                    timestamp=datetime.now(),
                    progress_info=final_progress
                )

        except Exception as e:
            error_msg = str(e)
            return StreamResult(
                url=str(request.url),
                status_code=0,
                headers={},
                bytes_downloaded=bytes_downloaded,
                total_bytes=None,
                output_path=request.output_path,
                response_time=time.time() - start_time,
                timestamp=datetime.now(),
                error=error_msg
            )


# Enhanced convenience functions

async def download_file(
    url: str,
    output_path: Path,
    chunk_size: int = 8192,
    progress_callback: Optional[Callable[[ProgressInfo], None]] = None,
    config: Optional[FetchConfig] = None
) -> StreamResult:
    """
    Convenience function to download a file with progress tracking.

    Args:
        url: URL to download
        output_path: Path to save the downloaded file
        chunk_size: Size of chunks to read
        progress_callback: Optional callback for progress updates
        config: Optional configuration

    Returns:
        StreamResult with download information
    """
    streaming_config = StreamingConfig(
        chunk_size=chunk_size,
        enable_progress=progress_callback is not None
    )

    request = StreamRequest(
        url=url,
        output_path=output_path,
        streaming_config=streaming_config
    )

    async with StreamingWebFetcher(config) as fetcher:
        return await fetcher.stream_fetch(request, progress_callback)


async def fetch_with_cache(
    url: str,
    content_type: ContentType = ContentType.TEXT,
    cache_config: Optional[Any] = None,  # CacheConfig from utils
    config: Optional[FetchConfig] = None
) -> FetchResult:
    """
    Convenience function to fetch a URL with caching.

    Args:
        url: URL to fetch
        content_type: How to parse the response content
        cache_config: Cache configuration
        config: Optional fetch configuration

    Returns:
        FetchResult with response data
    """
    from .utils import SimpleCache, CacheConfig

    if cache_config is None:
        cache_config = CacheConfig()

    # Create a simple cache instance
    cache = SimpleCache(cache_config)

    # Check cache first
    cached_entry = cache.get(url)
    if cached_entry:
        # Decompress if needed
        content = cached_entry.response_data
        if cached_entry.compressed:
            import gzip
            content = gzip.decompress(content)
            if content_type in (ContentType.TEXT, ContentType.JSON, ContentType.HTML):
                content = content.decode('utf-8')

        return FetchResult(
            url=url,
            status_code=cached_entry.status_code,
            headers=cached_entry.headers,
            content=content,
            content_type=content_type,
            response_time=0.0,  # Cached response
            timestamp=cached_entry.timestamp
        )

    # Fetch from network
    request = FetchRequest(url=url, content_type=content_type)

    async with WebFetcher(config) as fetcher:
        result = await fetcher.fetch_single(request)

        # Cache successful responses
        if result.is_success:
            cache.put(url, result.content, result.headers, result.status_code)

        return result


# URL validation convenience functions

def is_valid_url(url: str) -> bool:
    """
    Check if a URL is valid.

    Args:
        url: URL string to validate

    Returns:
        True if URL is valid, False otherwise
    """
    from .utils import URLValidator
    return URLValidator.is_valid_url(url)


def normalize_url(url: str, base_url: Optional[str] = None) -> str:
    """
    Normalize a URL by resolving relative paths and cleaning up.

    Args:
        url: URL to normalize
        base_url: Base URL for resolving relative URLs

    Returns:
        Normalized URL string
    """
    from .utils import URLValidator
    return URLValidator.normalize_url(url, base_url)


def analyze_url(url: str):  # Returns URLAnalysis
    """
    Perform comprehensive URL analysis.

    Args:
        url: URL to analyze

    Returns:
        URLAnalysis object with detailed information
    """
    from .utils import URLValidator
    return URLValidator.analyze_url(url)


def analyze_headers(headers: Dict[str, str]):  # Returns HeaderAnalysis
    """
    Analyze HTTP response headers.

    Args:
        headers: Dictionary of response headers

    Returns:
        HeaderAnalysis object with parsed header information
    """
    from .utils import ResponseAnalyzer
    return ResponseAnalyzer.analyze_headers(headers)


def detect_content_type(headers: Dict[str, str], content: bytes) -> str:
    """
    Detect content type from headers and content.

    Args:
        headers: Response headers
        content: Response content bytes

    Returns:
        Detected content type string
    """
    from .utils import ResponseAnalyzer
    return ResponseAnalyzer.detect_content_type(headers, content)
