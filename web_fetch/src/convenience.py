"""
Convenience functions for quick web fetching operations.

This module provides simple, high-level functions for common web fetching tasks
without requiring explicit class instantiation or session management.
"""

from __future__ import annotations

from pathlib import Path
from typing import Any, Callable, List, Optional

from .core_fetcher import WebFetcher
from .streaming_fetcher import Streaming<PERSON>eb<PERSON>etcher
from ..models import (
    BatchFetchRequest,
    BatchFetchResult,
    ContentType,
    FetchConfig,
    FetchRequest,
    FetchResult,
    ProgressInfo,
    StreamingConfig,
    StreamRequest,
    StreamResult,
)


async def fetch_url(
    url: str,
    content_type: ContentType = ContentType.TEXT,
    config: Optional[FetchConfig] = None
) -> FetchResult:
    """
    Convenience function to fetch a single URL.

    Args:
        url: URL to fetch
        content_type: How to parse the response content
        config: Optional configuration

    Returns:
        FetchResult with response data
    """
    request = FetchRequest(url=url, content_type=content_type)

    async with WebFetcher(config) as fetcher:
        return await fetcher.fetch_single(request)


async def fetch_urls(
    urls: List[str],
    content_type: ContentType = ContentType.TEXT,
    config: Optional[FetchConfig] = None
) -> BatchFetchResult:
    """
    Convenience function to fetch multiple URLs concurrently.

    Args:
        urls: List of URLs to fetch
        content_type: How to parse the response content
        config: Optional configuration

    Returns:
        BatchFetchResult with all results and statistics
    """
    requests = [FetchRequest(url=url, content_type=content_type) for url in urls]
    batch_request = BatchFetchRequest(requests=requests, config=config)

    async with WebFetcher(config) as fetcher:
        return await fetcher.fetch_batch(batch_request)


async def download_file(
    url: str,
    output_path: Path,
    chunk_size: int = 8192,
    progress_callback: Optional[Callable[[ProgressInfo], None]] = None,
    config: Optional[FetchConfig] = None
) -> StreamResult:
    """
    Convenience function to download a file with progress tracking.

    Args:
        url: URL to download
        output_path: Path to save the downloaded file
        chunk_size: Size of chunks to read
        progress_callback: Optional callback for progress updates
        config: Optional configuration

    Returns:
        StreamResult with download information
    """
    streaming_config = StreamingConfig(
        chunk_size=chunk_size,
        enable_progress=progress_callback is not None
    )

    request = StreamRequest(
        url=url,
        output_path=output_path,
        streaming_config=streaming_config
    )

    async with StreamingWebFetcher(config) as fetcher:
        return await fetcher.stream_fetch(request, progress_callback)


async def fetch_with_cache(
    url: str,
    content_type: ContentType = ContentType.TEXT,
    cache_config: Optional[Any] = None,  # CacheConfig from utils
    config: Optional[FetchConfig] = None
) -> FetchResult:
    """
    Convenience function to fetch a URL with caching.

    Args:
        url: URL to fetch
        content_type: How to parse the response content
        cache_config: Cache configuration
        config: Optional fetch configuration

    Returns:
        FetchResult with response data
    """
    from ..utils import SimpleCache, CacheConfig

    if cache_config is None:
        cache_config = CacheConfig()

    # Create a simple cache instance
    cache = SimpleCache(cache_config)

    # Check cache first
    cached_entry = cache.get(url)
    if cached_entry:
        # Decompress if needed
        content = cached_entry.response_data
        if cached_entry.compressed:
            import gzip
            content = gzip.decompress(content)
            if content_type in (ContentType.TEXT, ContentType.JSON, ContentType.HTML):
                content = content.decode('utf-8')

        return FetchResult(
            url=url,
            status_code=cached_entry.status_code,
            headers=cached_entry.headers,
            content=content,
            content_type=content_type,
            response_time=0.0,  # Cached response
            timestamp=cached_entry.timestamp
        )

    # Fetch from network
    request = FetchRequest(url=url, content_type=content_type)

    async with WebFetcher(config) as fetcher:
        result = await fetcher.fetch_single(request)

        # Cache successful responses
        if result.is_success:
            cache.put(url, result.content, result.headers, result.status_code)

        return result
