"""
HTTP protocol implementation for the web_fetch library.

This module provides HTTP/HTTPS fetching capabilities with modern async/await syntax,
proper session management, and comprehensive error handling.
"""

from .core import WebFetcher
from .streaming import StreamingWebFetcher
from .convenience import (
    fetch_url,
    fetch_urls, 
    download_file,
    fetch_with_cache,
)

__all__ = [
    "WebFetcher",
    "StreamingWebFetcher",
    "fetch_url", 
    "fetch_urls",
    "download_file",
    "fetch_with_cache",
]
