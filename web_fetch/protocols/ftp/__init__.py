"""
FTP protocol implementation for the web_fetch library.

This module provides comprehensive FTP functionality including connection pooling,
file operations, streaming downloads, parallel processing, and file verification.
"""

from .core import (
    FTPFetcher,
    ftp_download_file,
    ftp_download_batch,
    ftp_list_directory, 
    ftp_get_file_info,
)

__all__ = [
    "FTPFetcher",
    "ftp_download_file",
    "ftp_download_batch",
    "ftp_list_directory",
    "ftp_get_file_info", 
]
